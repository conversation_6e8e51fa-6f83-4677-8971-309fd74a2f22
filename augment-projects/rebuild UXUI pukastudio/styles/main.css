/* Custom styles for Puka Studio */

/* Anime-inspired animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px rgba(107, 70, 193, 0.5); }
  50% { box-shadow: 0 0 20px rgba(107, 70, 193, 0.8), 0 0 30px rgba(236, 72, 153, 0.5); }
}

@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

/* Floating animation for hero elements */
.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* Glow effect for special elements */
.glow-effect {
  animation: glow 2s ease-in-out infinite alternate;
}

/* Shimmer loading effect */
.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Dark mode shimmer */
.dark .shimmer {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #6B46C1, #EC4899);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5b21b6, #db2777);
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-track {
  background: #374151;
}

.dark ::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #8b5cf6, #f472b6);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #7c3aed, #ec4899);
}

/* Mobile menu animations */
.mobile-menu-enter {
  transform: translateX(-100%);
  opacity: 0;
}

.mobile-menu-enter-active {
  transform: translateX(0);
  opacity: 1;
  transition: transform 0.3s ease-out, opacity 0.3s ease-out;
}

.mobile-menu-exit {
  transform: translateX(0);
  opacity: 1;
}

.mobile-menu-exit-active {
  transform: translateX(-100%);
  opacity: 0;
  transition: transform 0.3s ease-in, opacity 0.3s ease-in;
}

/* Product card hover effects */
.product-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 25px -5px rgba(107, 70, 193, 0.1), 0 10px 10px -5px rgba(107, 70, 193, 0.04);
}

/* Image lazy loading placeholder */
.image-placeholder {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Dark mode image placeholder */
.dark .image-placeholder {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
}

/* Focus styles for accessibility */
.focus-visible {
  outline: 2px solid #6B46C1;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn-primary {
    background-color: #000;
    border-color: #000;
    color: #fff;
  }

  .btn-outline {
    border-color: #000;
    color: #000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    color-scheme: dark;
  }
}

/* Theme transition support */
* {
  transition-property: background-color, border-color, color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* Disable transitions during theme change to prevent flash */
.theme-transition-disable * {
  transition: none !important;
}

/* Fix aspect ratio issues - override problematic aspect-w-1 class */
.aspect-w-1 {
  padding-bottom: 1.5rem !important; /* 24px equivalent */
}

/* Enhanced product image sizing with responsive design */
.product-image-container {
  height: 24rem; /* 384px - h-96 equivalent */
  width: 100%;
}

.product-thumbnail {
  height: 5rem; /* 80px - h-20 equivalent */
  width: 100%;
}

/* Enhanced mobile navigation */
@media (max-width: 768px) {
  .nav-link-enhanced {
    @apply px-3 py-2 text-base;
  }

  .dropdown-item-enhanced {
    @apply p-4 text-base;
  }
}

/* Enhanced product card animations */
.product-size-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-size-card:hover {
  transform: translateY(-2px) scale(1.02);
}

/* Enhanced breadcrumb styling */
.breadcrumb-enhanced {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

/* Improved focus states for accessibility */
.focus-enhanced:focus {
  outline: 3px solid rgba(107, 70, 193, 0.5);
  outline-offset: 2px;
  border-radius: 8px;
}

/* Compact spacing utilities for better visual flow */
.compact-spacing {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.compact-section {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

/* Minimal gap between navigation and content */
.nav-content-gap {
  margin-top: 0;
  padding-top: 0.25rem;
}

/* Ultra-compact layout for product pages */
.ultra-compact {
  margin: 0;
  padding: 0.25rem 0;
}

/* Enhanced accessibility and contrast for option cards */
.option-card {
  transition: all 0.2s ease-in-out;
  border-width: 1px;
  border-style: solid;
}

.option-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.option-card-weapon {
  border-color: rgb(196 181 253); /* purple-300 */
  background-color: rgb(255 255 255);
}

.option-card-weapon:hover {
  border-color: rgb(147 51 234); /* purple-600 */
}

.option-card-shoe {
  border-color: rgb(134 239 172); /* green-300 */
  background-color: rgb(255 255 255);
}

.option-card-shoe:hover {
  border-color: rgb(34 197 94); /* green-500 */
}

/* Improved text truncation with tooltips */
.truncate-with-tooltip {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: help;
}

/* Status badges with better contrast */
.status-badge {
  font-weight: 600;
  letter-spacing: 0.025em;
  border: 1px solid transparent;
}

.status-available {
  background-color: rgb(220 252 231); /* green-100 */
  color: rgb(20 83 45); /* green-900 */
  border-color: rgb(134 239 172); /* green-300 */
}

.status-unavailable {
  background-color: rgb(254 226 226); /* red-100 */
  color: rgb(127 29 29); /* red-900 */
  border-color: rgb(252 165 165); /* red-300 */
}

/* Flexible image aspect ratios to prevent cropping */
.aspect-w-4 {
  position: relative;
  width: 100%;
}

.aspect-w-4::before {
  content: '';
  display: block;
  padding-bottom: 125%; /* 4:5 aspect ratio */
}

.aspect-h-5 > * {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.aspect-w-3 {
  position: relative;
  width: 100%;
}

.aspect-w-3::before {
  content: '';
  display: block;
  padding-bottom: 133.33%; /* 3:4 aspect ratio */
}

.aspect-h-4 > * {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Enhanced image container for better display */
.image-container {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  max-height: 600px;
}

/* Dark mode image container */
.dark .image-container {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
}

.image-container img {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
}

/* Responsive image adjustments */
@media (max-width: 640px) {
  .image-container {
    min-height: 300px;
    max-height: 400px;
  }
}

@media (min-width: 1024px) {
  .image-container {
    min-height: 500px;
    max-height: 700px;
  }
}

/* Square aspect ratio for thumbnails */
.aspect-square {
  aspect-ratio: 1 / 1;
}

/* Fallback for browsers that don't support aspect-ratio */
@supports not (aspect-ratio: 1 / 1) {
  .aspect-square {
    position: relative;
    width: 100%;
  }

  .aspect-square::before {
    content: '';
    display: block;
    padding-bottom: 100%;
  }

  .aspect-square > * {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

/* Enhanced image loading states */
.image-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

/* Dark mode image loading */
.dark .image-loading {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Image error state */
.image-error {
  background-color: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 0.875rem;
}

/* Dark mode image error */
.dark .image-error {
  background-color: #374151;
  color: #6b7280;
}

.image-error::before {
  content: '🖼️ Image not available';
}

/* Enhanced Search UX Animations */
@keyframes slide-down {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(-5px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes pulse-dot {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes search-focus {
  from {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.5);
  }
  to {
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  }
}

.animate-slide-down {
  animation: slide-down 0.2s ease-out;
}

.animate-fade-in {
  animation: fade-in 0.15s ease-out;
}

.animate-pulse-dot {
  animation: pulse-dot 1.5s ease-in-out infinite;
}

.animate-search-focus {
  animation: search-focus 0.3s ease-out;
}

/* Search input enhancements */
.search-input-enhanced {
  transition: all 0.2s ease-in-out;
}

.search-input-enhanced:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Search suggestions enhancements */
.search-suggestion-item {
  transition: all 0.15s ease-in-out;
  position: relative;
}

.search-suggestion-item:hover {
  transform: translateX(2px);
}

.search-suggestion-item.selected {
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Typing indicator */
.typing-indicator {
  background: linear-gradient(90deg,
    rgba(59, 130, 246, 0.1) 0%,
    rgba(59, 130, 246, 0.2) 50%,
    rgba(59, 130, 246, 0.1) 100%);
  background-size: 200% 100%;
  animation: typing-wave 1.5s ease-in-out infinite;
}

@keyframes typing-wave {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
